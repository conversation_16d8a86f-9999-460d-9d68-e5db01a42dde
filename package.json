{"name": "bluex", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:start": "docker compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/js": "^9.29.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@types/node": "^22.15.32", "@unocss/core": "^66.2.3", "@unocss/extractor-svelte": "^66.2.0", "@unocss/preset-icons": "^66.2.3", "@unocss/preset-wind3": "^66.2.3", "@unocss/svelte-scoped": "^66.2.0", "drizzle-kit": "^0.31.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.3", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "svelte": "^5.34.7", "svelte-check": "^4.2.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "unocss": "^66.2.3", "vite": "^6.3.5", "vite-plugin-devtools-json": "^0.2.0"}, "dependencies": {"@inlang/paraglide-js": "^2.0.13", "drizzle-orm": "^0.44.2", "postgres": "^3.4.7"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}